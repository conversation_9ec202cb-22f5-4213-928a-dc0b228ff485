"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { ModelConfiguration } from "./interfaces";

interface ModelConfigurationFieldProps {
  modelConfigs: ModelConfiguration[];
  updateModelConfigs: (modelConfigs: ModelConfiguration[]) => void;
}

export const ModelConfigurationField = ({
  modelConfigs,
  updateModelConfigs,
}: ModelConfigurationFieldProps) => {
  const handleUpdate = (index: number, newConfig: ModelConfiguration) => {
    const newModelConfigs = [...modelConfigs];
    newModelConfigs[index] = newConfig;
    updateModelConfigs(newModelConfigs);
  };

  return (
    <div className="w-full">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Model Name</TableHead>
            <TableHead>Max Input Tokens</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {modelConfigs.map((modelConfig, index) => (
            <TableRow key={index}>
              <TableCell>{modelConfig.model_name}</TableCell>
              <TableCell>
                <Input
                  type="number"
                  placeholder="Default"
                  value={modelConfig.max_input_tokens?.toString() || ""}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    const value = e.target.value;
                    handleUpdate(index, {
                      ...modelConfig,
                      max_input_tokens: value ? parseInt(value) : null,
                    });
                  }}
                />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};
