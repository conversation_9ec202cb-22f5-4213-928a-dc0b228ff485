#!/usr/bin/env python3
"""
Test script to verify that the GPT 4.1 model validation fix works correctly.
This script tests the model validation logic in process_message.py.
"""

import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from unittest.mock import Mock, patch
from onyx.llm.llm_provider_options import fetch_models_for_provider
from onyx.db.models import LLMProvider as LLMProviderModel

def test_fetch_models_for_provider():
    """Test that fetch_models_for_provider returns the expected models for OpenAI."""
    models = fetch_models_for_provider("openai")
    print(f"OpenAI models from fetch_models_for_provider: {models}")
    
    # Check that gpt-4.1 is in the list
    assert "gpt-4.1" in models, f"gpt-4.1 not found in OpenAI models: {models}"
    print("✓ gpt-4.1 is correctly included in OpenAI models")

def test_model_validation_logic():
    """Test the model validation logic that was fixed."""
    # Create a mock provider with null model_names (simulating database state)
    mock_provider = Mock(spec=LLMProviderModel)
    mock_provider.name = "openai"
    mock_provider.provider = "openai"
    mock_provider.model_names = None  # This is the key issue - null in database
    mock_provider.default_model_name = "gpt-4o"
    
    # Test the fixed validation logic
    model_version = "gpt-4.1"
    
    # Get the available model names for this provider
    # If model_names is null in the database, fall back to the well-known models
    available_models = (
        mock_provider.model_names 
        or fetch_models_for_provider(mock_provider.provider)
        or [mock_provider.default_model_name]
    )
    
    print(f"Available models for validation: {available_models}")
    
    # This should NOT raise an error now
    if model_version not in available_models:
        raise ValueError(f"Invalid model or provider given!")
    
    print("✓ Model validation logic works correctly for gpt-4.1")

def test_model_validation_with_populated_models():
    """Test that validation still works when model_names is populated."""
    # Create a mock provider with populated model_names
    mock_provider = Mock(spec=LLMProviderModel)
    mock_provider.name = "openai"
    mock_provider.provider = "openai"
    mock_provider.model_names = ["gpt-4o", "gpt-4.1", "gpt-3.5-turbo"]
    mock_provider.default_model_name = "gpt-4o"
    
    model_version = "gpt-4.1"
    
    # Get the available model names for this provider
    available_models = (
        mock_provider.model_names 
        or fetch_models_for_provider(mock_provider.provider)
        or [mock_provider.default_model_name]
    )
    
    print(f"Available models (populated): {available_models}")
    
    # This should NOT raise an error
    if model_version not in available_models:
        raise ValueError(f"Invalid model or provider given!")
    
    print("✓ Model validation logic works correctly with populated model_names")

def test_invalid_model_still_fails():
    """Test that invalid models still fail validation."""
    mock_provider = Mock(spec=LLMProviderModel)
    mock_provider.name = "openai"
    mock_provider.provider = "openai"
    mock_provider.model_names = None
    mock_provider.default_model_name = "gpt-4o"
    
    model_version = "invalid-model-name"
    
    # Get the available model names for this provider
    available_models = (
        mock_provider.model_names 
        or fetch_models_for_provider(mock_provider.provider)
        or [mock_provider.default_model_name]
    )
    
    print(f"Available models for invalid test: {available_models}")
    
    # This SHOULD raise an error
    try:
        if model_version not in available_models:
            raise ValueError(f"Invalid model or provider given!")
        assert False, "Expected validation to fail for invalid model"
    except ValueError as e:
        print(f"✓ Invalid model correctly rejected: {e}")

if __name__ == "__main__":
    print("Testing GPT 4.1 model validation fix...")
    print("=" * 50)
    
    try:
        test_fetch_models_for_provider()
        print()
        
        test_model_validation_logic()
        print()
        
        test_model_validation_with_populated_models()
        print()
        
        test_invalid_model_still_fails()
        print()
        
        print("=" * 50)
        print("✅ All tests passed! The GPT 4.1 validation fix is working correctly.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
