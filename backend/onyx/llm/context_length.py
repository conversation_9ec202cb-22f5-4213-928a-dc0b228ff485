from sqlalchemy.orm import Session

from onyx.configs.app_configs import GEN_AI_MAX_TOKENS
from onyx.configs.app_configs import GEN_AI_MODEL_FALLBACK_MAX_TOKENS
from onyx.db.llm import fetch_provider
from onyx.llm.llm_provider_options import get_max_input_tokens


def get_max_input_tokens_from_llm_provider(
    provider_name: str, model_name: str, db_session: Session
) -> int:
    """
    Returns the context length for the given model.
    1. Check if there is a value set in the provider's configuration in the DB
    2. Fall back to the get_max_input_tokens function
    """
    llm_provider = fetch_provider(db_session, provider_name)
    if llm_provider:
        for model_config in llm_provider.model_configs:
            if model_config.model_name == model_name and model_config.max_input_tokens:
                return model_config.max_input_tokens

    # Fall back to the old method
    return get_max_input_tokens(
        model_name=model_name,
        # if the env var isn't set, fallback to the model cost map
        # if that isn't set, then fallback to the final fallback value
        env_max_tokens=GEN_AI_MAX_TOKENS
        or GEN_AI_MODEL_FALLBACK_MAX_TOKENS,
    )
